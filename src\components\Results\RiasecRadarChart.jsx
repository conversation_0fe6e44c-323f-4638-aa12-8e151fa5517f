import { useState } from 'react';
import { motion } from 'framer-motion';
import { ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, Tooltip, Legend } from 'recharts';

const RiasecRadarChart = ({ data }) => {
  const [showExplanation, setShowExplanation] = useState(false);

  // Transform RIASEC data for radar chart
  const chartData = data ? [
    { trait: 'Realistic', value: data.realistic || 0, fullName: 'Realistic (Doer)' },
    { trait: 'Investigative', value: data.investigative || 0, fullName: 'Investigative (Thinker)' },
    { trait: 'Artistic', value: data.artistic || 0, fullName: 'Artistic (Creator)' },
    { trait: 'Social', value: data.social || 0, fullName: 'Social (Helper)' },
    { trait: 'Enterprising', value: data.enterprising || 0, fullName: 'Enterprising (Persuader)' },
    { trait: 'Conventional', value: data.conventional || 0, fullName: 'Conventional (Organizer)' }
  ] : [];

  const riasecExplanations = {
    realistic: {
      title: "Realistic (Doer)",
      description: "Prefer hands-on work, practical activities, and working with tools, machines, or animals.",
      careers: ["Engineer", "Mechanic", "Farmer", "Carpenter", "Pilot"]
    },
    investigative: {
      title: "Investigative (Thinker)", 
      description: "Enjoy research, analysis, and solving complex problems through systematic investigation.",
      careers: ["Scientist", "Researcher", "Doctor", "Analyst", "Programmer"]
    },
    artistic: {
      title: "Artistic (Creator)",
      description: "Value creativity, self-expression, and working in unstructured environments.",
      careers: ["Designer", "Writer", "Artist", "Musician", "Actor"]
    },
    social: {
      title: "Social (Helper)",
      description: "Prefer helping, teaching, and working with people to solve problems.",
      careers: ["Teacher", "Counselor", "Social Worker", "Nurse", "Therapist"]
    },
    enterprising: {
      title: "Enterprising (Persuader)",
      description: "Enjoy leading, persuading, and managing others to achieve goals.",
      careers: ["Manager", "Sales Rep", "Entrepreneur", "Lawyer", "Executive"]
    },
    conventional: {
      title: "Conventional (Organizer)",
      description: "Prefer structured work, following procedures, and organizing data.",
      careers: ["Accountant", "Administrator", "Banker", "Secretary", "Clerk"]
    }
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const traitKey = label.toLowerCase();
      const explanation = riasecExplanations[traitKey];
      
      return (
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white p-4 rounded-lg shadow-lg border border-gray-200 max-w-xs"
        >
          <h4 className="font-semibold text-gray-900 mb-2">{explanation?.title}</h4>
          <p className="text-sm text-gray-600 mb-2">{explanation?.description}</p>
          <div className="text-lg font-bold text-indigo-600 mb-2">
            Score: {data.value}
          </div>
          <div className="text-xs text-gray-500">
            <strong>Common careers:</strong> {explanation?.careers.join(', ')}
          </div>
        </motion.div>
      );
    }
    return null;
  };

  if (!data) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">RIASEC Holland Codes</h3>
        <p className="text-gray-600">No RIASEC data available.</p>
      </div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
    >
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900">RIASEC Holland Codes</h3>
        <button
          onClick={() => setShowExplanation(!showExplanation)}
          className="text-indigo-600 hover:text-indigo-800 text-sm font-medium flex items-center"
        >
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          {showExplanation ? 'Hide' : 'Learn More'}
        </button>
      </div>

      {showExplanation && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mb-6 p-4 bg-indigo-50 rounded-lg"
        >
          <h4 className="font-semibold text-indigo-900 mb-2">About RIASEC</h4>
          <p className="text-sm text-indigo-800 mb-3">
            The RIASEC model, developed by psychologist John Holland, categorizes people and work environments into six types. 
            Your scores indicate your preferences and strengths in each area.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-xs">
            {Object.entries(riasecExplanations).map(([key, explanation]) => (
              <div key={key} className="bg-white p-2 rounded border">
                <strong className="text-indigo-900">{explanation.title}:</strong>
                <span className="text-gray-700 ml-1">{explanation.description}</span>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <RadarChart data={chartData}>
            <PolarGrid stroke="#e5e7eb" />
            <PolarAngleAxis 
              dataKey="trait" 
              tick={{ fontSize: 12, fill: '#374151' }}
              className="text-sm font-medium"
            />
            <PolarRadiusAxis 
              angle={90} 
              domain={[0, 100]} 
              tick={{ fontSize: 10, fill: '#6b7280' }}
            />
            <Radar
              name="RIASEC Scores"
              dataKey="value"
              stroke="#4f46e5"
              fill="#4f46e5"
              fillOpacity={0.2}
              strokeWidth={2}
              dot={{ fill: '#4f46e5', strokeWidth: 2, r: 4 }}
            />
            <Tooltip content={<CustomTooltip />} />
          </RadarChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-3">
        {chartData.map((item, index) => (
          <motion.div
            key={item.trait}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="text-center p-2 bg-gray-50 rounded"
          >
            <div className="text-lg font-bold text-indigo-600">{item.value}</div>
            <div className="text-xs text-gray-600">{item.trait}</div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default RiasecRadarChart;
