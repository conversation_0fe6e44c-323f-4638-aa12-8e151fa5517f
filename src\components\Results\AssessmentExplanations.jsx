import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const AssessmentExplanations = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const assessmentInfo = {
    overview: {
      title: "Understanding Your Assessment Results",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            Your talent mapping assessment combines three scientifically-validated frameworks to provide 
            a comprehensive view of your personality, interests, and character strengths.
          </p>
          <div className="grid md:grid-cols-3 gap-4">
            <div className="bg-indigo-50 p-4 rounded-lg">
              <h4 className="font-semibold text-indigo-900 mb-2">RIASEC</h4>
              <p className="text-sm text-indigo-800">Career interests and work environment preferences</p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h4 className="font-semibold text-purple-900 mb-2">Big Five (OCEAN)</h4>
              <p className="text-sm text-purple-800">Core personality traits and behavioral tendencies</p>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">VIA Strengths</h4>
              <p className="text-sm text-blue-800">Character strengths and positive qualities</p>
            </div>
          </div>
        </div>
      )
    },
    riasec: {
      title: "RIASEC Holland Codes",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            Developed by psychologist John Holland, the RIASEC model identifies six personality types 
            that correspond to different work environments and career paths.
          </p>
          <div className="space-y-3">
            <div className="border-l-4 border-indigo-500 pl-4">
              <h4 className="font-semibold text-gray-900">How to interpret your scores:</h4>
              <ul className="text-sm text-gray-700 mt-2 space-y-1">
                <li>• <strong>High scores (70-100):</strong> Strong preference and natural fit</li>
                <li>• <strong>Medium scores (40-69):</strong> Moderate interest and capability</li>
                <li>• <strong>Low scores (0-39):</strong> Limited interest or preference</li>
              </ul>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">Your Holland Code</h4>
              <p className="text-sm text-gray-700">
                Your top 3 scores form your "Holland Code" - a three-letter combination that represents 
                your primary career interests. This code can help guide career exploration and decision-making.
              </p>
            </div>
          </div>
        </div>
      )
    },
    ocean: {
      title: "Big Five (OCEAN) Personality",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            The Big Five model is the most widely accepted personality framework in psychology, 
            measuring five broad dimensions that remain relatively stable throughout life.
          </p>
          <div className="space-y-3">
            <div className="border-l-4 border-purple-500 pl-4">
              <h4 className="font-semibold text-gray-900">Understanding your scores:</h4>
              <ul className="text-sm text-gray-700 mt-2 space-y-1">
                <li>• <strong>High scores (70-100):</strong> Strong expression of this trait</li>
                <li>• <strong>Average scores (30-69):</strong> Balanced or situational expression</li>
                <li>• <strong>Low scores (0-29):</strong> Less prominent trait expression</li>
              </ul>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">No "Good" or "Bad" Scores</h4>
              <p className="text-sm text-gray-700">
                Each trait has advantages in different situations. High and low scores both have 
                strengths and potential challenges. The key is understanding how to leverage your 
                natural tendencies effectively.
              </p>
            </div>
          </div>
        </div>
      )
    },
    via: {
      title: "VIA Character Strengths",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700 leading-relaxed">
            The VIA (Values in Action) Survey identifies your signature character strengths - 
            the positive traits that energize you and represent your authentic self.
          </p>
          <div className="space-y-3">
            <div className="border-l-4 border-blue-500 pl-4">
              <h4 className="font-semibold text-gray-900">Your Signature Strengths:</h4>
              <ul className="text-sm text-gray-700 mt-2 space-y-1">
                <li>• <strong>Top 5 strengths:</strong> Your core strengths to leverage and develop</li>
                <li>• <strong>Middle strengths:</strong> Strengths you can call upon when needed</li>
                <li>• <strong>Lesser strengths:</strong> Areas for potential growth or support from others</li>
              </ul>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">Using Your Strengths</h4>
              <p className="text-sm text-gray-700">
                Research shows that people who use their signature strengths regularly are more 
                engaged, perform better, and experience greater well-being. Focus on finding ways 
                to apply your top strengths in your work and personal life.
              </p>
            </div>
          </div>
        </div>
      )
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: '📊' },
    { id: 'riasec', label: 'RIASEC', icon: '🎯' },
    { id: 'ocean', label: 'Big Five', icon: '🌊' },
    { id: 'via', label: 'VIA Strengths', icon: '💪' }
  ];

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.6 }}
      className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
    >
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {assessmentInfo[activeTab].title}
            </h3>
            {assessmentInfo[activeTab].content}
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Quick Tips */}
      <div className="bg-gray-50 px-6 py-4 border-t border-gray-200">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-900">💡 Quick Tip</h4>
            <p className="text-sm text-gray-600 mt-1">
              {activeTab === 'overview' && "Hover over chart elements to see detailed explanations and career suggestions."}
              {activeTab === 'riasec' && "Look for careers that match your top 2-3 RIASEC codes for the best fit."}
              {activeTab === 'ocean' && "Consider how your personality traits align with different work environments and team dynamics."}
              {activeTab === 'via' && "Try to use your top 5 character strengths daily for increased satisfaction and performance."}
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default AssessmentExplanations;
