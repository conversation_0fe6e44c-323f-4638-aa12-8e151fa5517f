import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Cell } from 'recharts';

const OceanBarChart = ({ data }) => {
  const [showExplanation, setShowExplanation] = useState(false);

  // Transform OCEAN data for bar chart
  const chartData = data ? [
    { 
      trait: 'Openness', 
      value: data.openness || 0, 
      fullName: 'Openness to Experience',
      color: '#8b5cf6'
    },
    { 
      trait: 'Conscientiousness', 
      value: data.conscientiousness || 0, 
      fullName: 'Conscientiousness',
      color: '#06b6d4'
    },
    { 
      trait: 'Extraversion', 
      value: data.extraversion || 0, 
      fullName: 'Extraversion',
      color: '#10b981'
    },
    { 
      trait: 'Agreeableness', 
      value: data.agreeableness || 0, 
      fullName: 'Agreeableness',
      color: '#f59e0b'
    },
    { 
      trait: 'Neuroticism', 
      value: data.neuroticism || 0, 
      fullName: 'Neuroticism',
      color: '#ef4444'
    }
  ] : [];

  const oceanExplanations = {
    openness: {
      title: "Openness to Experience",
      description: "Reflects imagination, creativity, and willingness to try new things.",
      high: "Creative, curious, open-minded, enjoys variety and new experiences",
      low: "Practical, conventional, prefers routine and familiar experiences",
      careers: ["Artist", "Researcher", "Consultant", "Entrepreneur"]
    },
    conscientiousness: {
      title: "Conscientiousness",
      description: "Measures organization, responsibility, and self-discipline.",
      high: "Organized, reliable, hardworking, goal-oriented, disciplined",
      low: "Flexible, spontaneous, may struggle with deadlines and organization",
      careers: ["Manager", "Accountant", "Engineer", "Administrator"]
    },
    extraversion: {
      title: "Extraversion",
      description: "Indicates energy level, sociability, and assertiveness.",
      high: "Outgoing, energetic, talkative, enjoys social situations",
      low: "Reserved, quiet, prefers solitude, thoughtful before speaking",
      careers: ["Sales", "Teacher", "Public Relations", "Event Coordinator"]
    },
    agreeableness: {
      title: "Agreeableness", 
      description: "Reflects cooperation, trust, and concern for others.",
      high: "Cooperative, trusting, helpful, empathetic, team-oriented",
      low: "Competitive, skeptical, direct, independent, analytical",
      careers: ["Counselor", "Social Worker", "Nurse", "Human Resources"]
    },
    neuroticism: {
      title: "Neuroticism",
      description: "Measures emotional stability and stress management.",
      high: "Sensitive to stress, experiences strong emotions, may worry frequently",
      low: "Calm, emotionally stable, resilient, handles stress well",
      careers: ["Crisis Management", "Emergency Services", "Leadership Roles"]
    }
  };

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0];
      const traitKey = label.toLowerCase();
      const explanation = oceanExplanations[traitKey];
      
      return (
        <motion.div 
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white p-4 rounded-lg shadow-lg border border-gray-200 max-w-sm"
        >
          <h4 className="font-semibold text-gray-900 mb-2">{explanation?.title}</h4>
          <p className="text-sm text-gray-600 mb-3">{explanation?.description}</p>
          <div className="text-lg font-bold mb-2" style={{ color: data.payload.color }}>
            Score: {data.value}
          </div>
          <div className="text-xs text-gray-500 space-y-1">
            <div><strong>High scores:</strong> {explanation?.high}</div>
            <div><strong>Low scores:</strong> {explanation?.low}</div>
            <div><strong>Related careers:</strong> {explanation?.careers.join(', ')}</div>
          </div>
        </motion.div>
      );
    }
    return null;
  };

  if (!data) {
    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Big Five (OCEAN) Personality</h3>
        <p className="text-gray-600">No OCEAN data available.</p>
      </div>
    );
  }

  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
    >
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900">Big Five (OCEAN) Personality</h3>
        <button
          onClick={() => setShowExplanation(!showExplanation)}
          className="text-purple-600 hover:text-purple-800 text-sm font-medium flex items-center"
        >
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
          {showExplanation ? 'Hide' : 'Learn More'}
        </button>
      </div>

      {showExplanation && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mb-6 p-4 bg-purple-50 rounded-lg"
        >
          <h4 className="font-semibold text-purple-900 mb-2">About the Big Five</h4>
          <p className="text-sm text-purple-800 mb-3">
            The Big Five model is the most widely accepted personality framework in psychology. 
            It measures five broad dimensions of personality that remain relatively stable throughout life.
          </p>
          <div className="grid grid-cols-1 gap-3 text-xs">
            {Object.entries(oceanExplanations).map(([key, explanation]) => (
              <div key={key} className="bg-white p-3 rounded border">
                <strong className="text-purple-900">{explanation.title}:</strong>
                <p className="text-gray-700 mt-1">{explanation.description}</p>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
            <XAxis 
              dataKey="trait" 
              tick={{ fontSize: 12, fill: '#374151' }}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis 
              domain={[0, 100]}
              tick={{ fontSize: 10, fill: '#6b7280' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="value" radius={[4, 4, 0, 0]}>
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>

      <div className="mt-4 grid grid-cols-1 md:grid-cols-5 gap-3">
        {chartData.map((item, index) => (
          <motion.div
            key={item.trait}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
            className="text-center p-3 bg-gray-50 rounded"
          >
            <div 
              className="text-lg font-bold mb-1" 
              style={{ color: item.color }}
            >
              {item.value}
            </div>
            <div className="text-xs text-gray-600 font-medium">{item.trait}</div>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default OceanBarChart;
